package cfg

import (
	"fmt"
	"go-micro.dev/v4/config"
	"runtime"
)

type clientFlag struct {
	ApiPath  string `json:"api_path"`
	Platform string `json:"platform"`
	ClientId string `json:"client_id"`
	Secret   string `json:"secret"`
}

type EmailInfo struct {
	SmtpHost  string `json:"smtp_host"`
	SmtpPort  string `json:"smtp_port"`
	Username  string `json:"username"`
	Password  string `json:"password"`
	MaxClient int    `json:"max_client"`
}

type CronUrl struct {
	PocUpdateUrl        string `json:"poc_update_url"`
	EventUpdateUrl      string `json:"event_update_url"`
	EventUpdateUrlToken string `json:"event_update_url_token"`
}

type Local struct {
	Enable            bool `json:"enable"`
	CompanyCount      int  `json:"company_count"`
	OtherCompanyCount int  `json:"other_company_count"`
	OtherClueCount    int  `json:"other_clue_count"`
}

type AutoMISPConfig struct {
	Enabled           bool     `yaml:"enabled" json:"enabled"`
	RunTime           string   `yaml:"run_time" json:"run_time"`
	Receiver          []string `yaml:"receiver" json:"receiver"`
	Source            string   `yaml:"source" json:"source"`
	Subject           string   `yaml:"subject" json:"subject"`
	IncludeAttachment bool     `yaml:"include_attachment" json:"include_attachment"`
	EncryptKey        string   `yaml:"encrypt_key" json:"encrypt_key"`
}

type SMTPConfig struct {
	Host     string `yaml:"host" json:"host"`
	Port     int    `yaml:"port" json:"port"`
	Username string `yaml:"username" json:"username"`
	Password string `yaml:"password" json:"password"`
	From     string `yaml:"from" json:"from"`
	SSL      bool   `yaml:"ssl" json:"ssl"`
}

type GlobalEmailConfig struct {
	AutoMISP *AutoMISPConfig `json:"auto_misp"`
	SMTP     *SMTPConfig     `json:"smtp"`
}

var (
	GlobalAutoMISPConfig *AutoMISPConfig
	GlobalSMTPConfig     *SMTPConfig
	DingtalkAccessToken  string
	DingtalkSecret       string
)

type Common struct {
	Proxy                   bool            `json:"proxy"`
	ProxyKey                string          `json:"proxy_key"`
	ProxySecret             string          `json:"proxy_secret"`
	ProxyOrder              string          `json:"proxy_order"`
	ChromePath              string          `json:"chrome_path"`
	GitHubTokens            []string        `json:"github_tokens"`
	GiteeTokens             []string        `json:"gitee_tokens"`
	GitcodeTokens           []string        `json:"gitcode_tokens"`
	Network                 string          `json:"network"`
	JaegerAdder             string          `json:"jaeger_adder"`
	RootStorage             string          `json:"root_storage"`
	Client                  clientFlag      `json:"client"`
	ClueCacheDay            uint64          `json:"clue_cache_day"`
	AssetCacheDay           uint64          `json:"asset_cache_day"`
	AppCacheDay             uint64          `json:"app_cache_day"`
	OfficialAccountCacheDay uint64          `json:"official_account_cache_day"`
	DataLeakCacheDay        uint64          `json:"data_leak_cache_day"`
	IcpCacheDay             uint64          `json:"icp_cache_day"`
	WhoisCacheDay           uint64          `json:"whois_cache_day"`
	IcpCompanyFilter        []string        `json:"icp_company_filter"`
	Email                   EmailInfo       `json:"email"`
	CronUrl                 CronUrl         `json:"cron_url"`
	Token                   string          `json:"token"`
	DaShengPanToken         []string        `json:"da_sheng_pan_token"`
	Local                   Local           `json:"local"`
	AES256Key               string          `json:"aes_256_key"`
	AES256Nonce             string          `json:"aes_256_nonce"`
	AccountApplyByHandUid   int64           `json:"account_apply_by_hand_uid"`
	DingtalkAccessToken     string          `json:"dingtalk_access_token"`
	DingtalkSecret          string          `json:"dingtalk_secret"`
	FofaIcp                 bool            `json:"fofa_icp"`
	FofaMcp                 bool            `json:"fofa_mcp"`
	Node                    string          `json:"node"`
	MonitorAssetScanIpv6    bool            `json:"monitor_asset_scan_ipv6"`
	Env                     string          `json:"env"`
	PythonPath              string          `json:"python_path"`
	OneForAllPath           string          `json:"one_for_all_path"`
	GolangSwitchJob         bool            `json:"golang_switch_job"`
	ScanInsideIp            bool            `json:"scan_inside_ip"`
	WebUrl                  string          `json:"web_url"`
	GoscannerPath           string          `json:"goscanner_path"`
	AutoMISP                *AutoMISPConfig `json:"auto_misp"`
	SMTP                    *SMTPConfig     `json:"smtp"`
}

// LoadCommon 加载Common配置
func LoadCommon() Common {
	// _ = config.Get("common").Scan(&GetInstance().Common)
	RefCfgDefVal()
	return GetInstance().Common
}

func IsLocalClient() bool {
	return LoadCommon().Client.Platform == "local"
}

// IsLocalArm 判断是否本地化ARM架构
func IsLocalArm() bool {
	isArm := runtime.GOARCH == "arm64" || runtime.GOARCH == "arm"
	return IsLocalClient() && isArm
}

func ExecGolangJob() bool {
	return LoadCommon().GolangSwitchJob == true
}

func CanScanInsideIp() bool {
	return LoadCommon().ScanInsideIp == true
}

func GetWebUrl() string {
	webUrl := LoadCommon().WebUrl
	if webUrl == "" {
		return "https://foradar.baimaohui.net"
	} else {
		return webUrl
	}
}

// GetGoScanner 获取GoScanner地址
func GetGoScanner() string {
	if LoadCommon().GoscannerPath == "" {
		return "/data/foradar/goscanner"
	}
	return LoadCommon().GoscannerPath
}

// getDefaultEmailConfig 获取默认的邮件配置
func getDefaultEmailConfig() GlobalEmailConfig {
	return GlobalEmailConfig{
		AutoMISP: &AutoMISPConfig{
			Enabled:           true,
			RunTime:           "18:15",
			Receiver:          []string{"<EMAIL>", "<EMAIL>", "<EMAIL>"},
			Source:            "fofa",
			Subject:           "[每日自动推送] FOFA威胁情报",
			IncludeAttachment: true,
			EncryptKey:        "Tsgz@1234",
		},
		SMTP: &SMTPConfig{
			Host:     "smtp.exmail.qq.com",
			Port:     465,
			Username: "<EMAIL>",
			Password: "TSEfcmEVr65GJmiJ",
			From:     "<EMAIL>",
			SSL:      true,
		},
	}
}

func LoadCommonConfigFromConsul() error {
	// 本地化不需要这个配置项
	if LoadCommon().Client.Platform == "local" {
		fmt.Printf("[LoadCommonConfigFromConsul] 本地化环境，跳过配置加载\n")
		return nil
	}

	fmt.Printf("[LoadCommonConfigFromConsul] 开始从Consul加载配置\n")
	val := config.Get("common")

	// 添加调试日志：打印原始配置数据
	if rawData := val.Bytes(); len(rawData) > 0 {
		fmt.Printf("[LoadCommonConfigFromConsul] 原始配置数据长度: %d bytes\n", len(rawData))
		fmt.Printf("[LoadCommonConfigFromConsul] 原始配置数据: %s\n", string(rawData))
	} else {
		fmt.Printf("[LoadCommonConfigFromConsul] 警告: 未获取到任何配置数据\n")
	}

	// 尝试解析完整的Common结构体来对比
	var fullCommon Common
	if err := val.Scan(&fullCommon); err != nil {
		fmt.Printf("[LoadCommonConfigFromConsul] 扫描完整Common配置失败: %v\n", err)
	} else {
		fmt.Printf("[LoadCommonConfigFromConsul] 完整Common配置中的AutoMISP=%v, SMTP=%v\n",
			fullCommon.AutoMISP != nil, fullCommon.SMTP != nil)
		if fullCommon.AutoMISP != nil {
			fmt.Printf("[LoadCommonConfigFromConsul] 完整配置中的AutoMISP: %+v\n", *fullCommon.AutoMISP)
		}
		if fullCommon.SMTP != nil {
			fmt.Printf("[LoadCommonConfigFromConsul] 完整配置中的SMTP: %+v\n", *fullCommon.SMTP)
		}
	}

	var partial GlobalEmailConfig
	if err := val.Scan(&partial); err != nil {
		fmt.Printf("[LoadCommonConfigFromConsul] 扫描配置失败: %v，使用默认配置\n", err)

		// 扫描失败时使用默认配置
		partial = getDefaultEmailConfig()

		fmt.Printf("[LoadCommonConfigFromConsul] 扫描失败，使用默认配置: AutoMISP=%+v, SMTP=%+v\n",
			*partial.AutoMISP, *partial.SMTP)
	}

	// 添加调试日志：打印解析后的结构体
	fmt.Printf("[LoadCommonConfigFromConsul] 解析后的配置: AutoMISP=%v, SMTP=%v\n", partial.AutoMISP != nil, partial.SMTP != nil)
	if partial.AutoMISP != nil {
		fmt.Printf("[LoadCommonConfigFromConsul] AutoMISP配置: %+v\n", *partial.AutoMISP)
	}
	if partial.SMTP != nil {
		fmt.Printf("[LoadCommonConfigFromConsul] SMTP配置: %+v\n", *partial.SMTP)
	}

	if partial.AutoMISP == nil || partial.SMTP == nil {
		fmt.Printf("[LoadCommonConfigFromConsul] 配置缺失: AutoMISP=%v, SMTP=%v，使用默认配置\n", partial.AutoMISP == nil, partial.SMTP == nil)

		// 使用默认配置作为兜底方案
		defaultConfig := getDefaultEmailConfig()
		if partial.AutoMISP == nil {
			partial.AutoMISP = defaultConfig.AutoMISP
			fmt.Printf("[LoadCommonConfigFromConsul] 使用默认AutoMISP配置: %+v\n", *partial.AutoMISP)
		}

		if partial.SMTP == nil {
			partial.SMTP = defaultConfig.SMTP
			fmt.Printf("[LoadCommonConfigFromConsul] 使用默认SMTP配置: %+v\n", *partial.SMTP)
		}
	}

	GlobalAutoMISPConfig = partial.AutoMISP
	GlobalSMTPConfig = partial.SMTP
	fmt.Printf("[LoadCommonConfigFromConsul] 配置加载成功\n")
	return nil
}

func LoadDingtalkConfigFromConsul() error {
	// 本地化不需要这个配置项
	if LoadCommon().Client.Platform == "local" {
		fmt.Printf("[LoadDingtalkConfigFromConsul] 本地化环境，跳过配置加载\n")
		return nil
	}

	fmt.Printf("[LoadDingtalkConfigFromConsul] 开始从Consul加载钉钉配置\n")
	val := config.Get("common")
	var commonCfg Common
	if err := val.Scan(&commonCfg); err != nil {
		fmt.Printf("[LoadDingtalkConfigFromConsul] 扫描配置失败: %v\n", err)
		return fmt.Errorf("failed to scan config/common into Common struct: %w", err)
	}

	fmt.Printf("[LoadDingtalkConfigFromConsul] 钉钉配置: Secret=%s, AccessToken=%s\n",
		commonCfg.DingtalkSecret, commonCfg.DingtalkAccessToken)

	DingtalkSecret = commonCfg.DingtalkSecret
	DingtalkAccessToken = commonCfg.DingtalkAccessToken
	fmt.Printf("[LoadDingtalkConfigFromConsul] 钉钉配置加载成功\n")
	return nil
}
